<?php
/**
 * AJAX Handler for Out of Stock Options Module
 */

require_once(dirname(__FILE__) . '/../../config/config.inc.php');
require_once(dirname(__FILE__) . '/../../init.php');
require_once(dirname(__FILE__) . '/stoutofstockoptions.php');

// Check if module is active
if (!Module::isEnabled('stoutofstockoptions')) {
    http_response_code(404);
    exit;
}

// Verify token
if (!Tools::getToken(false)) {
    http_response_code(403);
    exit;
}

$action = Tools::getValue('action');
$response = ['success' => false, 'message' => ''];

switch ($action) {
    case 'subscribe_reminder':
        $response = handleReminderSubscription();
        break;
        
    case 'check_stock':
        $response = checkProductStock();
        break;
        
    default:
        $response['message'] = 'Invalid action';
        break;
}

header('Content-Type: application/json');
echo json_encode($response);
exit;

function handleReminderSubscription()
{
    $email = Tools::getValue('email');
    $id_product = (int)Tools::getValue('id_product');
    $id_product_attribute = (int)Tools::getValue('id_product_attribute') ?: null;
    
    // Validate input
    if (!Validate::isEmail($email)) {
        return ['success' => false, 'message' => 'Invalid email address'];
    }
    
    if (!$id_product || !Validate::isUnsignedId($id_product)) {
        return ['success' => false, 'message' => 'Invalid product'];
    }
    
    // Check if product exists
    $product = new Product($id_product);
    if (!Validate::isLoadedObject($product)) {
        return ['success' => false, 'message' => 'Product not found'];
    }
    
    // Check if already subscribed
    $sql = 'SELECT id_subscription FROM `' . _DB_PREFIX_ . 'st_reminder_subscriptions` 
            WHERE `id_product` = ' . (int)$id_product . '
            AND `email` = "' . pSQL($email) . '"';
    
    if ($id_product_attribute) {
        $sql .= ' AND `id_product_attribute` = ' . (int)$id_product_attribute;
    } else {
        $sql .= ' AND `id_product_attribute` IS NULL';
    }
    
    $existing = Db::getInstance()->getValue($sql);
    
    if ($existing) {
        return ['success' => false, 'message' => 'You are already subscribed for this product'];
    }
    
    // Insert subscription
    $data = [
        'id_product' => $id_product,
        'id_product_attribute' => $id_product_attribute,
        'email' => pSQL($email),
        'date_add' => date('Y-m-d H:i:s'),
        'notified' => 0
    ];
    
    $result = Db::getInstance()->insert('st_reminder_subscriptions', $data);
    
    if ($result) {
        $message = Configuration::get('ST_SUCCESS_MESSAGE', Context::getContext()->language->id);
        if (!$message) {
            $message = 'You will be notified when this product is available.';
        }
        
        return ['success' => true, 'message' => $message];
    } else {
        return ['success' => false, 'message' => 'Failed to subscribe. Please try again.'];
    }
}

function checkProductStock()
{
    $id_product = (int)Tools::getValue('id_product');
    $combination = Tools::getValue('combination');
    
    if (!$id_product || !Validate::isUnsignedId($id_product)) {
        return ['success' => false, 'message' => 'Invalid product'];
    }
    
    $product = new Product($id_product);
    if (!Validate::isLoadedObject($product)) {
        return ['success' => false, 'message' => 'Product not found'];
    }
    
    $id_product_attribute = null;
    
    // If combination is provided, find the matching product attribute
    if ($combination && is_array($combination)) {
        $id_product_attribute = findProductAttributeId($id_product, $combination);
    }
    
    // Check stock
    $quantity = StockAvailable::getQuantityAvailableByProduct($id_product, $id_product_attribute);
    $out_of_stock = $quantity <= 0;
    
    return [
        'success' => true,
        'out_of_stock' => $out_of_stock,
        'quantity' => $quantity,
        'id_product_attribute' => $id_product_attribute
    ];
}

function findProductAttributeId($id_product, $combination)
{
    $product = new Product($id_product);
    $combinations = $product->getAttributeCombinations(Context::getContext()->language->id);
    
    foreach ($combinations as $comb) {
        $match = true;
        foreach ($combination as $attr_id => $value_id) {
            if ($comb['id_attribute'] != $value_id) {
                $match = false;
                break;
            }
        }
        if ($match) {
            return $comb['id_product_attribute'];
        }
    }
    
    return null;
}
