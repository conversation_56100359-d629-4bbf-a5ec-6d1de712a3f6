# Out of Stock Options Module for PrestaShop

## Overview

The **stoutofstockoptions** module enhances your PrestaShop store by giving you full control over how out-of-stock products and product attributes are displayed. Instead of showing a plain "Out of Stock" message, you can configure labels, colors, reminder options, and advanced behavior for both products and combinations.

## Features

### Module Control
- ✅ Enable/Disable the entire module

### Custom Out of Stock Labels
- Display out-of-stock labels on products automatically
- Set your own text for labels (e.g., Coming Soon, Out of Stock, Notify Me)
- Multilingual support for labels

### Cart Button Handling
- Option to enable/disable the Add to Cart button when a product is out of stock
- Replace it with a custom message or reminder button

### Reminder Button
- Show a "Remind Me" button for out-of-stock products
- Customers can subscribe to be notified when the product becomes available
- Option to set custom success messages (multilingual support)
- Email notifications when products are back in stock

### Styling Controls
- Choose label colors (background/text)
- Position labels on product image: Top / Middle / Bottom
- Apply custom CSS classes for further design customization

### Product & Attribute Behavior
- Enable/disable entire product or specific product attributes if out of stock
- If the default combination goes out of stock, automatically set another available combination as default

### Advanced Features
- Hide out-of-stock combinations in dropdowns
- Option to hide quantity selector when product is out of stock
- Choose where to display the reminder button (below image, inside description, next to label, after add-to-cart button)
- Multi-shop compatible
- Multi-language support for labels and messages

## Installation

1. Upload the module folder to `/modules/` directory in your PrestaShop installation
2. Go to **Modules > Module Manager** in your PrestaShop admin
3. Search for "Out of Stock Options"
4. Click **Install**
5. Configure the module settings

## Configuration

### General Settings
- **Enable Module**: Turn the entire module functionality on/off

### Label Settings
- **Show Out of Stock Labels**: Display custom labels on out-of-stock products
- **Custom Label Text**: Set custom text for each language
- **Label Position**: Choose where to position labels (Top/Middle/Bottom)
- **Label Colors**: Customize background and text colors

### Cart & Reminder Settings
- **Hide Add to Cart Button**: Hide the cart button for out-of-stock products
- **Show Reminder Button**: Enable email reminder functionality
- **Reminder Button Position**: Choose where to display the reminder button
- **Success Message**: Custom message after successful subscription

### Advanced Settings
- **Hide Out-of-Stock Combinations**: Hide unavailable combinations in dropdowns
- **Hide Quantity Selector**: Hide quantity input when out of stock
- **Auto Switch Default Combination**: Automatically change default combination when current goes out of stock

## File Structure

```
stoutofstockoptions/
├── stoutofstockoptions.php          # Main module file
├── config.xml                       # Module configuration
├── ajax.php                         # AJAX handler for frontend requests
├── classes/
│   └── StReminderSubscription.php   # Model for reminder subscriptions
├── controllers/
│   └── admin/
│       └── AdminStReminderSubscriptionsController.php
├── views/
│   ├── css/
│   │   ├── front.css                # Frontend styles
│   │   └── admin.css                # Admin styles
│   ├── js/
│   │   └── front.js                 # Frontend JavaScript
│   └── templates/
│       └── hook/
│           ├── out-of-stock-label.tpl
│           └── product-actions.tpl
├── mails/
│   └── en/
│       ├── restock_notification.html
│       └── restock_notification.txt
└── translations/
    └── en.php                       # English translations
```

## Database Tables

The module creates two database tables:

### `st_reminder_subscriptions`
Stores customer email subscriptions for out-of-stock products.

### `st_product_settings`
Stores custom settings for individual products.

## Hooks Used

- `displayProductActions`: Show reminder buttons and custom actions
- `displayProductAdditionalInfo`: Display out-of-stock labels
- `displayHeader`: Include CSS and JavaScript files
- `actionProductUpdate`: Handle product updates
- `actionUpdateQuantity`: Handle stock quantity changes
- `displayAdminProductsExtra`: Admin product form extensions

## Customization

### CSS Classes
The module adds several CSS classes that you can customize:

- `.st-out-of-stock-label`: Main label styling
- `.st-position-top/middle/bottom`: Label positioning
- `.st-reminder-btn`: Reminder button styling
- `.st-hide-add-to-cart`: Applied when cart button should be hidden
- `.st-hide-quantity`: Applied when quantity selector should be hidden

### Template Overrides
You can override the module templates by copying them to your theme:

```
themes/your-theme/modules/stoutofstockoptions/views/templates/hook/
```

## Compatibility

- **PrestaShop Version**: ******* and higher
- **PHP Version**: 7.1 and higher
- **Multi-shop**: Yes
- **Multi-language**: Yes

## Support

For support and customization requests, please contact the module developer.

## License

This module is released under the Academic Free License (AFL 3.0).

## Changelog

### Version 1.0.0
- Initial release
- Basic out-of-stock label functionality
- Email reminder system
- Admin configuration interface
- Multi-language support
- Advanced product behavior options
