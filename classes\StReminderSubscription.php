<?php
/**
 * Model class for Reminder Subscriptions
 */

class StReminderSubscription extends ObjectModel
{
    public $id_subscription;
    public $id_product;
    public $id_product_attribute;
    public $email;
    public $date_add;
    public $notified;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'st_reminder_subscriptions',
        'primary' => 'id_subscription',
        'fields' => [
            'id_product' => [
                'type' => self::TYPE_INT,
                'validate' => 'isUnsignedId',
                'required' => true
            ],
            'id_product_attribute' => [
                'type' => self::TYPE_INT,
                'validate' => 'isUnsignedId',
                'required' => false
            ],
            'email' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isEmail',
                'required' => true,
                'size' => 255
            ],
            'date_add' => [
                'type' => self::TYPE_DATE,
                'validate' => 'isDate',
                'required' => true
            ],
            'notified' => [
                'type' => self::TYPE_BOOL,
                'validate' => 'isBool',
                'required' => false
            ]
        ]
    ];

    public function __construct($id = null, $id_lang = null, $id_shop = null)
    {
        parent::__construct($id, $id_lang, $id_shop);
    }

    /**
     * Get all subscriptions for a specific product
     *
     * @param int $id_product
     * @param int|null $id_product_attribute
     * @param bool $only_not_notified
     * @return array
     */
    public static function getByProduct($id_product, $id_product_attribute = null, $only_not_notified = false)
    {
        $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'st_reminder_subscriptions` 
                WHERE `id_product` = ' . (int)$id_product;

        if ($id_product_attribute !== null) {
            $sql .= ' AND `id_product_attribute` = ' . (int)$id_product_attribute;
        }

        if ($only_not_notified) {
            $sql .= ' AND `notified` = 0';
        }

        $sql .= ' ORDER BY `date_add` DESC';

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Check if email is already subscribed for a product
     *
     * @param string $email
     * @param int $id_product
     * @param int|null $id_product_attribute
     * @return bool
     */
    public static function isSubscribed($email, $id_product, $id_product_attribute = null)
    {
        $sql = 'SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'st_reminder_subscriptions` 
                WHERE `email` = "' . pSQL($email) . '" 
                AND `id_product` = ' . (int)$id_product;

        if ($id_product_attribute !== null) {
            $sql .= ' AND `id_product_attribute` = ' . (int)$id_product_attribute;
        } else {
            $sql .= ' AND `id_product_attribute` IS NULL';
        }

        return (bool)Db::getInstance()->getValue($sql);
    }

    /**
     * Mark subscription as notified
     *
     * @return bool
     */
    public function markAsNotified()
    {
        $this->notified = 1;
        return $this->update();
    }

    /**
     * Get product name for this subscription
     *
     * @param int $id_lang
     * @return string
     */
    public function getProductName($id_lang = null)
    {
        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }

        $sql = 'SELECT pl.name FROM `' . _DB_PREFIX_ . 'product_lang` pl
                WHERE pl.id_product = ' . (int)$this->id_product . '
                AND pl.id_lang = ' . (int)$id_lang;

        return Db::getInstance()->getValue($sql);
    }

    /**
     * Get combination name for this subscription
     *
     * @param int $id_lang
     * @return string
     */
    public function getCombinationName($id_lang = null)
    {
        if (!$this->id_product_attribute) {
            return '';
        }

        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }

        $combination = new Combination($this->id_product_attribute);
        return $combination->getAttributesName($id_lang);
    }

    /**
     * Delete old notified subscriptions
     *
     * @param int $days_old
     * @return bool
     */
    public static function cleanOldNotifications($days_old = 30)
    {
        $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'st_reminder_subscriptions` 
                WHERE `notified` = 1 
                AND `date_add` < DATE_SUB(NOW(), INTERVAL ' . (int)$days_old . ' DAY)';

        return Db::getInstance()->execute($sql);
    }

    /**
     * Get subscription statistics
     *
     * @return array
     */
    public static function getStatistics()
    {
        $stats = [];

        // Total subscriptions
        $stats['total'] = (int)Db::getInstance()->getValue(
            'SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'st_reminder_subscriptions`'
        );

        // Pending notifications
        $stats['pending'] = (int)Db::getInstance()->getValue(
            'SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'st_reminder_subscriptions` WHERE `notified` = 0'
        );

        // Sent notifications
        $stats['sent'] = (int)Db::getInstance()->getValue(
            'SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'st_reminder_subscriptions` WHERE `notified` = 1'
        );

        // Today's subscriptions
        $stats['today'] = (int)Db::getInstance()->getValue(
            'SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'st_reminder_subscriptions` 
             WHERE DATE(`date_add`) = CURDATE()'
        );

        return $stats;
    }
}
