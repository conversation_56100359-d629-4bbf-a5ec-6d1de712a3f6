{*
* Product Actions Template for Out of Stock Products
*}

{if $show_reminder && $reminder_position == 'below_image'}
<div class="st-reminder-section st-position-below-image">
    <button type="button" class="btn btn-primary st-reminder-btn" 
            data-product-id="{$product.id_product}"
            data-product-attribute="{if isset($product.id_product_attribute)}{$product.id_product_attribute}{else}0{/if}">
        <i class="material-icons">notifications</i>
        {l s='Remind Me When Available' mod='stoutofstockoptions'}
    </button>
</div>
{/if}

{if $show_reminder && $reminder_position == 'after_add_to_cart'}
<div class="st-reminder-section st-position-after-cart">
    <button type="button" class="btn btn-outline-primary st-reminder-btn" 
            data-product-id="{$product.id_product}"
            data-product-attribute="{if isset($product.id_product_attribute)}{$product.id_product_attribute}{else}0{/if}">
        <i class="material-icons">email</i>
        {l s='Get Notified' mod='stoutofstockoptions'}
    </button>
</div>
{/if}

{if $show_reminder && $reminder_position == 'inside_description'}
<div class="st-reminder-section st-position-description">
    <div class="alert alert-info">
        <p>{l s='This product is currently out of stock.' mod='stoutofstockoptions'}</p>
        <button type="button" class="btn btn-primary st-reminder-btn" 
                data-product-id="{$product.id_product}"
                data-product-attribute="{if isset($product.id_product_attribute)}{$product.id_product_attribute}{else}0{/if}">
            {l s='Notify me when available' mod='stoutofstockoptions'}
        </button>
    </div>
</div>
{/if}

{* Reminder Modal *}
<div class="modal fade" id="stReminderModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{l s='Product Availability Reminder' mod='stoutofstockoptions'}</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="stReminderForm">
                    <div class="form-group">
                        <label for="reminderEmail">{l s='Email Address' mod='stoutofstockoptions'}</label>
                        <input type="email" class="form-control" id="reminderEmail" name="email" required>
                        <small class="form-text text-muted">
                            {l s='We will notify you when this product becomes available.' mod='stoutofstockoptions'}
                        </small>
                    </div>
                    <input type="hidden" id="reminderProductId" name="id_product">
                    <input type="hidden" id="reminderProductAttribute" name="id_product_attribute">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    {l s='Cancel' mod='stoutofstockoptions'}
                </button>
                <button type="button" class="btn btn-primary" id="submitReminder">
                    {l s='Subscribe' mod='stoutofstockoptions'}
                </button>
            </div>
        </div>
    </div>
</div>
