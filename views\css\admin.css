/**
 * Out of Stock Options - Admin Styles
 */

/* Module Configuration Form */
.st-config-form .form-group {
    margin-bottom: 20px;
}

.st-config-form .help-block {
    font-style: italic;
    color: #666;
    margin-top: 5px;
}

.st-config-form .panel-heading {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 6px 6px 0 0;
}

.st-config-form .panel-heading .panel-title {
    font-weight: 600;
}

/* Color Picker Styling */
.st-color-picker {
    display: inline-block;
    position: relative;
}

.st-color-picker input[type="color"] {
    width: 50px;
    height: 35px;
    border: 2px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    padding: 0;
}

.st-color-picker input[type="color"]:hover {
    border-color: #007cba;
}

/* Statistics Dashboard */
.st-stats-dashboard {
    display: flex;
    gap: 20px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.st-stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    flex: 1;
    min-width: 200px;
    text-align: center;
}

.st-stat-card .stat-number {
    font-size: 2.5em;
    font-weight: bold;
    color: #007cba;
    margin-bottom: 10px;
}

.st-stat-card .stat-label {
    color: #666;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.st-stat-card.pending .stat-number {
    color: #f39c12;
}

.st-stat-card.sent .stat-number {
    color: #27ae60;
}

.st-stat-card.today .stat-number {
    color: #8e44ad;
}

/* Admin Table Styling */
.st-admin-table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.st-admin-table .table {
    margin-bottom: 0;
}

.st-admin-table .table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.st-admin-table .table td {
    vertical-align: middle;
}

.st-admin-table .table tr:hover {
    background-color: #f8f9fa;
}

/* Action Buttons */
.st-action-btn {
    padding: 6px 12px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
    margin: 2px;
    transition: all 0.3s ease;
}

.st-action-btn.delete {
    background-color: #dc3545;
    color: white;
}

.st-action-btn.delete:hover {
    background-color: #c82333;
    color: white;
    text-decoration: none;
}

.st-action-btn.edit {
    background-color: #007bff;
    color: white;
}

.st-action-btn.edit:hover {
    background-color: #0056b3;
    color: white;
    text-decoration: none;
}

/* Bulk Actions */
.st-bulk-actions {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.st-bulk-actions select {
    margin-right: 10px;
}

/* Status Badges */
.st-status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.st-status-badge.notified {
    background-color: #d4edda;
    color: #155724;
}

.st-status-badge.pending {
    background-color: #fff3cd;
    color: #856404;
}

/* Form Sections */
.st-form-section {
    background: white;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.st-form-section h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #2c3e50;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 10px;
}

/* Toggle Switches */
.st-toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.st-toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.st-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.st-toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .st-toggle-slider {
    background-color: #007bff;
}

input:checked + .st-toggle-slider:before {
    transform: translateX(26px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .st-stats-dashboard {
        flex-direction: column;
    }
    
    .st-stat-card {
        min-width: auto;
    }
    
    .st-admin-table {
        overflow-x: auto;
    }
    
    .st-form-section {
        padding: 15px;
    }
}
