/**
 * Out of Stock Options - Frontend JavaScript
 */

$(document).ready(function() {
    
    // Initialize the module
    StOutOfStockOptions.init();
    
});

var StOutOfStockOptions = {
    
    init: function() {
        this.bindEvents();
        this.handleOutOfStockProducts();
    },
    
    bindEvents: function() {
        // Reminder button click
        $(document).on('click', '.st-reminder-btn', function(e) {
            e.preventDefault();
            var productId = $(this).data('product-id');
            var productAttribute = $(this).data('product-attribute') || 0;
            
            StOutOfStockOptions.showReminderModal(productId, productAttribute);
        });
        
        // Submit reminder form
        $(document).on('click', '#submitReminder', function(e) {
            e.preventDefault();
            StOutOfStockOptions.submitReminderForm();
        });
        
        // Handle modal close
        $('#stReminderModal').on('hidden.bs.modal', function() {
            StOutOfStockOptions.resetReminderForm();
        });
        
        // Handle product attribute changes
        $(document).on('change', '.product-variants select, .product-variants input[type="radio"]', function() {
            setTimeout(function() {
                StOutOfStockOptions.handleAttributeChange();
            }, 100);
        });
    },
    
    showReminderModal: function(productId, productAttribute) {
        $('#reminderProductId').val(productId);
        $('#reminderProductAttribute').val(productAttribute);
        $('#stReminderModal').modal('show');
        $('#reminderEmail').focus();
    },
    
    submitReminderForm: function() {
        var form = $('#stReminderForm');
        var email = $('#reminderEmail').val();
        var productId = $('#reminderProductId').val();
        var productAttribute = $('#reminderProductAttribute').val();
        
        // Basic validation
        if (!this.validateEmail(email)) {
            this.showMessage('Please enter a valid email address.', 'error');
            return;
        }
        
        // Show loading state
        $('#submitReminder').prop('disabled', true).text('Subscribing...');
        
        // AJAX request
        $.ajax({
            url: prestashop.urls.base_url + 'modules/stoutofstockoptions/ajax.php',
            type: 'POST',
            dataType: 'json',
            data: {
                action: 'subscribe_reminder',
                email: email,
                id_product: productId,
                id_product_attribute: productAttribute,
                token: prestashop.static_token
            },
            success: function(response) {
                if (response.success) {
                    StOutOfStockOptions.showMessage(response.message, 'success');
                    $('#stReminderModal').modal('hide');
                } else {
                    StOutOfStockOptions.showMessage(response.message, 'error');
                }
            },
            error: function() {
                StOutOfStockOptions.showMessage('An error occurred. Please try again.', 'error');
            },
            complete: function() {
                $('#submitReminder').prop('disabled', false).text('Subscribe');
            }
        });
    },
    
    resetReminderForm: function() {
        $('#stReminderForm')[0].reset();
        $('.st-message').hide();
    },
    
    validateEmail: function(email) {
        var re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },
    
    showMessage: function(message, type) {
        var messageHtml = '<div class="st-message ' + type + '">' + message + '</div>';
        
        // Remove existing messages
        $('.st-message').remove();
        
        // Add new message
        $('#stReminderModal .modal-body').prepend(messageHtml);
        $('.st-message').fadeIn();
        
        // Auto hide success messages
        if (type === 'success') {
            setTimeout(function() {
                $('.st-message').fadeOut();
            }, 3000);
        }
    },
    
    handleOutOfStockProducts: function() {
        // Hide add to cart buttons for out of stock products
        $('.st-hide-add-to-cart').each(function() {
            $(this).find('.add-to-cart, .product-add-to-cart').hide();
        });
        
        // Hide quantity selectors
        $('.st-hide-quantity').each(function() {
            $(this).find('.product-quantity, .qty').hide();
        });
        
        // Handle combination changes
        this.handleAttributeChange();
    },
    
    handleAttributeChange: function() {
        // This function handles when product attributes change
        // and updates the out of stock status accordingly
        
        var currentCombination = this.getCurrentCombination();
        if (currentCombination) {
            this.updateOutOfStockStatus(currentCombination);
        }
    },
    
    getCurrentCombination: function() {
        var combination = {};
        var hasAttributes = false;
        
        // Get selected attributes
        $('.product-variants select').each(function() {
            var attributeId = $(this).data('product-attribute');
            var value = $(this).val();
            if (attributeId && value) {
                combination[attributeId] = value;
                hasAttributes = true;
            }
        });
        
        $('.product-variants input[type="radio"]:checked').each(function() {
            var attributeId = $(this).data('product-attribute');
            var value = $(this).val();
            if (attributeId && value) {
                combination[attributeId] = value;
                hasAttributes = true;
            }
        });
        
        return hasAttributes ? combination : null;
    },
    
    updateOutOfStockStatus: function(combination) {
        // Check if current combination is out of stock
        $.ajax({
            url: prestashop.urls.base_url + 'modules/stoutofstockoptions/ajax.php',
            type: 'POST',
            dataType: 'json',
            data: {
                action: 'check_stock',
                combination: combination,
                id_product: $('#reminderProductId').val() || $('[data-product-id]').first().data('product-id'),
                token: prestashop.static_token
            },
            success: function(response) {
                if (response.out_of_stock) {
                    StOutOfStockOptions.showOutOfStockElements();
                } else {
                    StOutOfStockOptions.hideOutOfStockElements();
                }
            }
        });
    },
    
    showOutOfStockElements: function() {
        $('.st-out-of-stock-label').show();
        $('.st-reminder-section').show();
        
        // Hide add to cart if configured
        if ($('.product-container').hasClass('st-hide-add-to-cart')) {
            $('.add-to-cart, .product-add-to-cart').hide();
        }
        
        // Hide quantity if configured
        if ($('.product-container').hasClass('st-hide-quantity')) {
            $('.product-quantity, .qty').hide();
        }
    },
    
    hideOutOfStockElements: function() {
        $('.st-out-of-stock-label').hide();
        $('.st-reminder-section').hide();
        
        // Show add to cart
        $('.add-to-cart, .product-add-to-cart').show();
        
        // Show quantity
        $('.product-quantity, .qty').show();
    }
    
};
