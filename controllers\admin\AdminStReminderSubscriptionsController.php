<?php
/**
 * Admin Controller for Reminder Subscriptions
 */

class AdminStReminderSubscriptionsController extends ModuleAdminController
{
    public function __construct()
    {
        $this->table = 'st_reminder_subscriptions';
        $this->className = 'StReminderSubscription';
        $this->identifier = 'id_subscription';
        $this->bootstrap = true;
        $this->lang = false;

        parent::__construct();

        $this->addRowAction('delete');
        $this->bulk_actions = [
            'delete' => [
                'text' => $this->l('Delete selected'),
                'icon' => 'icon-trash',
                'confirm' => $this->l('Delete selected items?')
            ]
        ];

        $this->fields_list = [
            'id_subscription' => [
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs'
            ],
            'product_name' => [
                'title' => $this->l('Product'),
                'search' => false,
                'orderby' => false
            ],
            'email' => [
                'title' => $this->l('Email'),
                'search' => true
            ],
            'date_add' => [
                'title' => $this->l('Date Added'),
                'type' => 'datetime',
                'search' => false
            ],
            'notified' => [
                'title' => $this->l('Notified'),
                'type' => 'bool',
                'align' => 'center',
                'class' => 'fixed-width-xs'
            ]
        ];

        $this->_select = 'pl.name as product_name';
        $this->_join = 'LEFT JOIN `' . _DB_PREFIX_ . 'product_lang` pl ON (a.id_product = pl.id_product AND pl.id_lang = ' . (int)$this->context->language->id . ')';
        $this->_orderBy = 'date_add';
        $this->_orderWay = 'DESC';
    }

    public function setMedia()
    {
        parent::setMedia();
        $this->addCSS($this->module->getPathUri() . 'views/css/admin.css');
    }

    public function renderList()
    {
        $this->addRowActionSkipList('delete', []);

        return parent::renderList();
    }

    public function postProcess()
    {
        if (Tools::isSubmit('submitBulkdelete' . $this->table)) {
            $this->processBulkDelete();
        } elseif (Tools::isSubmit('delete' . $this->table)) {
            $this->processDelete();
        }

        return parent::postProcess();
    }

    protected function processBulkDelete()
    {
        if (is_array($this->boxes) && !empty($this->boxes)) {
            $object = new $this->className();

            if (isset($object->noZeroObject) &&
                in_array(0, $this->boxes) && count($this->boxes) > $object->noZeroObject) {
                $this->errors[] = Tools::displayError('You cannot delete this selection.');
            } else {
                $result = true;
                if ($this->deleted) {
                    foreach ($this->boxes as $id) {
                        $to_delete = new $this->className($id);
                        $to_delete->deleted = 1;
                        $result = $result && $to_delete->update();
                    }
                } else {
                    $result = $object->deleteSelection($this->boxes);
                }

                if ($result) {
                    $this->redirect_after = self::$currentIndex . '&conf=2&token=' . $this->token;
                } else {
                    $this->errors[] = Tools::displayError('An error occurred while deleting this selection.');
                }
            }
        } else {
            $this->errors[] = Tools::displayError('You must select at least one element to delete.');
        }
    }

    protected function processDelete()
    {
        if (Validate::isLoadedObject($object = $this->loadObject())) {
            if ($object->delete()) {
                $this->redirect_after = self::$currentIndex . '&conf=1&token=' . $this->token;
            } else {
                $this->errors[] = Tools::displayError('An error occurred during deletion.');
            }
        } else {
            $this->errors[] = Tools::displayError('An error occurred while deleting the object.');
        }
    }
}
