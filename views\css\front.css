/**
 * Out of Stock Options - Frontend Styles
 */

/* Out of Stock Labels */
.st-out-of-stock-label {
    position: absolute;
    z-index: 10;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.st-out-of-stock-label:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

/* Label Positions */
.st-position-top {
    top: 10px;
    left: 10px;
}

.st-position-middle {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.st-position-bottom {
    bottom: 10px;
    left: 10px;
}

/* <PERSON>minder <PERSON> */
.st-reminder-btn {
    margin: 10px 0;
    border-radius: 25px;
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.st-reminder-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.st-reminder-btn .material-icons {
    font-size: 18px;
}

/* Reminder Section Positions */
.st-position-below-image {
    margin: 15px 0;
    text-align: center;
}

.st-position-after-cart {
    margin-top: 15px;
}

.st-position-description {
    margin: 20px 0;
}

.st-position-description .alert {
    border-radius: 8px;
    border: none;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-left: 4px solid #2196f3;
}

/* Modal Styles */
#stReminderModal .modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

#stReminderModal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    border-bottom: none;
}

#stReminderModal .modal-header .close {
    color: white;
    opacity: 0.8;
    text-shadow: none;
}

#stReminderModal .modal-header .close:hover {
    opacity: 1;
}

#stReminderModal .form-control {
    border-radius: 8px;
    border: 2px solid #e0e0e0;
    padding: 12px 15px;
    transition: border-color 0.3s ease;
}

#stReminderModal .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

#stReminderModal .btn {
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 500;
}

/* Success/Error Messages */
.st-message {
    padding: 12px 20px;
    border-radius: 8px;
    margin: 15px 0;
    font-weight: 500;
    display: none;
}

.st-message.success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.st-message.error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

/* Product Page Specific */
.product-container .st-out-of-stock-label {
    position: absolute;
    right: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .st-out-of-stock-label {
        font-size: 10px;
        padding: 4px 8px;
    }
    
    .st-reminder-btn {
        width: 100%;
        justify-content: center;
        margin: 10px 0;
    }
    
    .st-position-middle {
        transform: translate(-50%, -50%) scale(0.9);
    }
}

/* Animation for label appearance */
@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.st-out-of-stock-label {
    animation: fadeInScale 0.3s ease-out;
}

/* Hide add to cart button when configured */
.st-hide-add-to-cart .add-to-cart,
.st-hide-add-to-cart .product-add-to-cart {
    display: none !important;
}

/* Hide quantity selector when configured */
.st-hide-quantity .product-quantity,
.st-hide-quantity .qty {
    display: none !important;
}
