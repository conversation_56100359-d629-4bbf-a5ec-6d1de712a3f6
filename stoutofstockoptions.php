<?php
/**
 * Out of Stock Options Module
 *
 * <AUTHOR> Name
 * @copyright 2024
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StOutOfStockOptions extends Module
{
    public function __construct()
    {
        $this->name = 'stoutofstockoptions';
        $this->tab = 'administration';
        $this->version = '1.0.0';
        $this->author = 'Your Name';
        $this->need_instance = 0;
        $this->ps_versions_compliancy = [
            'min' => '*******',
            'max' => _PS_VERSION_
        ];
        $this->bootstrap = true;

        parent::__construct();

        $this->displayName = $this->l('Out of Stock Options');
        $this->description = $this->l('Enhanced control over out-of-stock products with custom labels, colors, and reminder options.');
        $this->confirmUninstall = $this->l('Are you sure you want to uninstall this module?');

        if (!Configuration::get('ST_OUT_OF_STOCK_OPTIONS')) {
            $this->warning = $this->l('No name provided');
        }
    }

    public function install()
    {
        if (Shop::isFeatureActive()) {
            Shop::setContext(Shop::CONTEXT_ALL);
        }

        return parent::install() &&
            $this->registerHook('displayProductActions') &&
            $this->registerHook('displayProductAdditionalInfo') &&
            $this->registerHook('displayHeader') &&
            $this->registerHook('actionProductUpdate') &&
            $this->registerHook('actionUpdateQuantity') &&
            $this->registerHook('displayAdminProductsExtra') &&
            $this->createTables() &&
            $this->installConfiguration();
    }

    public function uninstall()
    {
        return parent::uninstall() &&
            $this->uninstallConfiguration() &&
            $this->dropTables();
    }

    protected function createTables()
    {
        $sql = [];

        // Table for reminder subscriptions
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_reminder_subscriptions` (
            `id_subscription` int(11) NOT NULL AUTO_INCREMENT,
            `id_product` int(11) NOT NULL,
            `id_product_attribute` int(11) DEFAULT NULL,
            `email` varchar(255) NOT NULL,
            `date_add` datetime NOT NULL,
            `notified` tinyint(1) DEFAULT 0,
            PRIMARY KEY (`id_subscription`),
            KEY `product_email` (`id_product`, `email`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        // Table for custom product settings
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_product_settings` (
            `id_product` int(11) NOT NULL,
            `custom_label` varchar(255) DEFAULT NULL,
            `label_color` varchar(7) DEFAULT NULL,
            `text_color` varchar(7) DEFAULT NULL,
            `hide_add_to_cart` tinyint(1) DEFAULT 0,
            `show_reminder` tinyint(1) DEFAULT 1,
            PRIMARY KEY (`id_product`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        foreach ($sql as $query) {
            if (!Db::getInstance()->execute($query)) {
                return false;
            }
        }

        return true;
    }

    protected function dropTables()
    {
        $sql = [
            'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_reminder_subscriptions`',
            'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_product_settings`'
        ];

        foreach ($sql as $query) {
            if (!Db::getInstance()->execute($query)) {
                return false;
            }
        }

        return true;
    }

    protected function installConfiguration()
    {
        $languages = Language::getLanguages(false);
        $default_labels = [];
        $default_messages = [];

        foreach ($languages as $lang) {
            $default_labels[$lang['id_lang']] = 'Out of Stock';
            $default_messages[$lang['id_lang']] = 'You will be notified when this product is available.';
        }

        return Configuration::updateValue('ST_OUT_OF_STOCK_OPTIONS', 1) &&
            Configuration::updateValue('ST_ENABLE_MODULE', 1) &&
            Configuration::updateValue('ST_SHOW_LABELS', 1) &&
            Configuration::updateValue('ST_CUSTOM_LABEL', $default_labels) &&
            Configuration::updateValue('ST_LABEL_POSITION', 'top') &&
            Configuration::updateValue('ST_LABEL_BG_COLOR', '#ff0000') &&
            Configuration::updateValue('ST_LABEL_TEXT_COLOR', '#ffffff') &&
            Configuration::updateValue('ST_HIDE_ADD_TO_CART', 1) &&
            Configuration::updateValue('ST_SHOW_REMINDER', 1) &&
            Configuration::updateValue('ST_REMINDER_POSITION', 'below_image') &&
            Configuration::updateValue('ST_SUCCESS_MESSAGE', $default_messages) &&
            Configuration::updateValue('ST_HIDE_COMBINATIONS', 1) &&
            Configuration::updateValue('ST_HIDE_QUANTITY', 1) &&
            Configuration::updateValue('ST_AUTO_SWITCH_DEFAULT', 1);
    }

    protected function uninstallConfiguration()
    {
        return Configuration::deleteByName('ST_OUT_OF_STOCK_OPTIONS') &&
            Configuration::deleteByName('ST_ENABLE_MODULE') &&
            Configuration::deleteByName('ST_SHOW_LABELS') &&
            Configuration::deleteByName('ST_CUSTOM_LABEL') &&
            Configuration::deleteByName('ST_LABEL_POSITION') &&
            Configuration::deleteByName('ST_LABEL_BG_COLOR') &&
            Configuration::deleteByName('ST_LABEL_TEXT_COLOR') &&
            Configuration::deleteByName('ST_HIDE_ADD_TO_CART') &&
            Configuration::deleteByName('ST_SHOW_REMINDER') &&
            Configuration::deleteByName('ST_REMINDER_POSITION') &&
            Configuration::deleteByName('ST_SUCCESS_MESSAGE') &&
            Configuration::deleteByName('ST_HIDE_COMBINATIONS') &&
            Configuration::deleteByName('ST_HIDE_QUANTITY') &&
            Configuration::deleteByName('ST_AUTO_SWITCH_DEFAULT');
    }

    public function getContent()
    {
        $output = null;

        if (Tools::isSubmit('submit' . $this->name)) {
            $output .= $this->postProcess();
        }

        return $output . $this->displayForm();
    }

    protected function displayForm()
    {
        // Get default language
        $default_lang = (int)Configuration::get('PS_LANG_DEFAULT');

        // Init Fields form array
        $fields_form = [];

        // General Settings
        $fields_form[0]['form'] = [
            'legend' => [
                'title' => $this->l('General Settings'),
                'icon' => 'icon-cogs'
            ],
            'input' => [
                [
                    'type' => 'switch',
                    'label' => $this->l('Enable Module'),
                    'name' => 'ST_ENABLE_MODULE',
                    'is_bool' => true,
                    'desc' => $this->l('Enable or disable the entire module functionality.'),
                    'values' => [
                        [
                            'id' => 'active_on',
                            'value' => true,
                            'label' => $this->l('Enabled')
                        ],
                        [
                            'id' => 'active_off',
                            'value' => false,
                            'label' => $this->l('Disabled')
                        ]
                    ],
                ],
            ],
            'submit' => [
                'title' => $this->l('Save'),
                'class' => 'btn btn-default pull-right'
            ]
        ];

        // Label Settings
        $fields_form[1]['form'] = [
            'legend' => [
                'title' => $this->l('Label Settings'),
                'icon' => 'icon-tag'
            ],
            'input' => [
                [
                    'type' => 'switch',
                    'label' => $this->l('Show Out of Stock Labels'),
                    'name' => 'ST_SHOW_LABELS',
                    'is_bool' => true,
                    'desc' => $this->l('Display custom labels on out-of-stock products.'),
                    'values' => [
                        [
                            'id' => 'labels_on',
                            'value' => true,
                            'label' => $this->l('Yes')
                        ],
                        [
                            'id' => 'labels_off',
                            'value' => false,
                            'label' => $this->l('No')
                        ]
                    ],
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Custom Label Text'),
                    'name' => 'ST_CUSTOM_LABEL',
                    'lang' => true,
                    'desc' => $this->l('Text to display on out-of-stock products (e.g., "Out of Stock", "Coming Soon").'),
                    'size' => 50
                ],
                [
                    'type' => 'select',
                    'label' => $this->l('Label Position'),
                    'name' => 'ST_LABEL_POSITION',
                    'desc' => $this->l('Where to position the label on product images.'),
                    'options' => [
                        'query' => [
                            ['id' => 'top', 'name' => $this->l('Top')],
                            ['id' => 'middle', 'name' => $this->l('Middle')],
                            ['id' => 'bottom', 'name' => $this->l('Bottom')]
                        ],
                        'id' => 'id',
                        'name' => 'name'
                    ]
                ],
                [
                    'type' => 'color',
                    'label' => $this->l('Label Background Color'),
                    'name' => 'ST_LABEL_BG_COLOR',
                    'desc' => $this->l('Background color for the out-of-stock label.'),
                    'size' => 10
                ],
                [
                    'type' => 'color',
                    'label' => $this->l('Label Text Color'),
                    'name' => 'ST_LABEL_TEXT_COLOR',
                    'desc' => $this->l('Text color for the out-of-stock label.'),
                    'size' => 10
                ],
            ],
            'submit' => [
                'title' => $this->l('Save'),
                'class' => 'btn btn-default pull-right'
            ]
        ];

        // Cart & Reminder Settings
        $fields_form[2]['form'] = [
            'legend' => [
                'title' => $this->l('Cart & Reminder Settings'),
                'icon' => 'icon-shopping-cart'
            ],
            'input' => [
                [
                    'type' => 'switch',
                    'label' => $this->l('Hide Add to Cart Button'),
                    'name' => 'ST_HIDE_ADD_TO_CART',
                    'is_bool' => true,
                    'desc' => $this->l('Hide the add to cart button for out-of-stock products.'),
                    'values' => [
                        [
                            'id' => 'hide_cart_on',
                            'value' => true,
                            'label' => $this->l('Yes')
                        ],
                        [
                            'id' => 'hide_cart_off',
                            'value' => false,
                            'label' => $this->l('No')
                        ]
                    ],
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Show Reminder Button'),
                    'name' => 'ST_SHOW_REMINDER',
                    'is_bool' => true,
                    'desc' => $this->l('Show a "Remind Me" button for out-of-stock products.'),
                    'values' => [
                        [
                            'id' => 'reminder_on',
                            'value' => true,
                            'label' => $this->l('Yes')
                        ],
                        [
                            'id' => 'reminder_off',
                            'value' => false,
                            'label' => $this->l('No')
                        ]
                    ],
                ],
                [
                    'type' => 'select',
                    'label' => $this->l('Reminder Button Position'),
                    'name' => 'ST_REMINDER_POSITION',
                    'desc' => $this->l('Where to display the reminder button.'),
                    'options' => [
                        'query' => [
                            ['id' => 'below_image', 'name' => $this->l('Below Image')],
                            ['id' => 'inside_description', 'name' => $this->l('Inside Description')],
                            ['id' => 'next_to_label', 'name' => $this->l('Next to Label')],
                            ['id' => 'after_add_to_cart', 'name' => $this->l('After Add to Cart')]
                        ],
                        'id' => 'id',
                        'name' => 'name'
                    ]
                ],
                [
                    'type' => 'textarea',
                    'label' => $this->l('Success Message'),
                    'name' => 'ST_SUCCESS_MESSAGE',
                    'lang' => true,
                    'desc' => $this->l('Message shown after successful reminder subscription.'),
                    'rows' => 3,
                    'cols' => 50
                ],
            ],
            'submit' => [
                'title' => $this->l('Save'),
                'class' => 'btn btn-default pull-right'
            ]
        ];

        // Advanced Settings
        $fields_form[3]['form'] = [
            'legend' => [
                'title' => $this->l('Advanced Settings'),
                'icon' => 'icon-cog'
            ],
            'input' => [
                [
                    'type' => 'switch',
                    'label' => $this->l('Hide Out-of-Stock Combinations'),
                    'name' => 'ST_HIDE_COMBINATIONS',
                    'is_bool' => true,
                    'desc' => $this->l('Hide out-of-stock combinations in product dropdowns.'),
                    'values' => [
                        [
                            'id' => 'hide_comb_on',
                            'value' => true,
                            'label' => $this->l('Yes')
                        ],
                        [
                            'id' => 'hide_comb_off',
                            'value' => false,
                            'label' => $this->l('No')
                        ]
                    ],
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Hide Quantity Selector'),
                    'name' => 'ST_HIDE_QUANTITY',
                    'is_bool' => true,
                    'desc' => $this->l('Hide quantity selector when product is out of stock.'),
                    'values' => [
                        [
                            'id' => 'hide_qty_on',
                            'value' => true,
                            'label' => $this->l('Yes')
                        ],
                        [
                            'id' => 'hide_qty_off',
                            'value' => false,
                            'label' => $this->l('No')
                        ]
                    ],
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Auto Switch Default Combination'),
                    'name' => 'ST_AUTO_SWITCH_DEFAULT',
                    'is_bool' => true,
                    'desc' => $this->l('Automatically set another combination as default when the current default goes out of stock.'),
                    'values' => [
                        [
                            'id' => 'auto_switch_on',
                            'value' => true,
                            'label' => $this->l('Yes')
                        ],
                        [
                            'id' => 'auto_switch_off',
                            'value' => false,
                            'label' => $this->l('No')
                        ]
                    ],
                ],
            ],
            'submit' => [
                'title' => $this->l('Save'),
                'class' => 'btn btn-default pull-right'
            ]
        ];

        $helper = new HelperForm();

        // Module, token and currentIndex
        $helper->module = $this;
        $helper->name_controller = $this->name;
        $helper->token = Tools::getAdminTokenLite('AdminModules');
        $helper->currentIndex = AdminController::$currentIndex . '&configure=' . $this->name;

        // Language
        $helper->default_form_language = $default_lang;
        $helper->allow_employee_form_lang = $default_lang;

        // Title and toolbar
        $helper->title = $this->displayName;
        $helper->show_toolbar = true;
        $helper->toolbar_scroll = true;
        $helper->submit_action = 'submit' . $this->name;
        $helper->toolbar_btn = [
            'save' => [
                'desc' => $this->l('Save'),
                'href' => AdminController::$currentIndex . '&configure=' . $this->name . '&save' . $this->name .
                    '&token=' . Tools::getAdminTokenLite('AdminModules'),
            ],
            'back' => [
                'href' => AdminController::$currentIndex . '&token=' . Tools::getAdminTokenLite('AdminModules'),
                'desc' => $this->l('Back to list')
            ]
        ];

        // Load current values
        $helper->fields_value['ST_ENABLE_MODULE'] = Configuration::get('ST_ENABLE_MODULE');
        $helper->fields_value['ST_SHOW_LABELS'] = Configuration::get('ST_SHOW_LABELS');
        $helper->fields_value['ST_CUSTOM_LABEL'] = Configuration::get('ST_CUSTOM_LABEL');
        $helper->fields_value['ST_LABEL_POSITION'] = Configuration::get('ST_LABEL_POSITION');
        $helper->fields_value['ST_LABEL_BG_COLOR'] = Configuration::get('ST_LABEL_BG_COLOR');
        $helper->fields_value['ST_LABEL_TEXT_COLOR'] = Configuration::get('ST_LABEL_TEXT_COLOR');
        $helper->fields_value['ST_HIDE_ADD_TO_CART'] = Configuration::get('ST_HIDE_ADD_TO_CART');
        $helper->fields_value['ST_SHOW_REMINDER'] = Configuration::get('ST_SHOW_REMINDER');
        $helper->fields_value['ST_REMINDER_POSITION'] = Configuration::get('ST_REMINDER_POSITION');
        $helper->fields_value['ST_SUCCESS_MESSAGE'] = Configuration::get('ST_SUCCESS_MESSAGE');
        $helper->fields_value['ST_HIDE_COMBINATIONS'] = Configuration::get('ST_HIDE_COMBINATIONS');
        $helper->fields_value['ST_HIDE_QUANTITY'] = Configuration::get('ST_HIDE_QUANTITY');
        $helper->fields_value['ST_AUTO_SWITCH_DEFAULT'] = Configuration::get('ST_AUTO_SWITCH_DEFAULT');

        return $helper->generateForm($fields_form);
    }

    protected function postProcess()
    {
        $form_values = [];
        $form_values['ST_ENABLE_MODULE'] = Tools::getValue('ST_ENABLE_MODULE');
        $form_values['ST_SHOW_LABELS'] = Tools::getValue('ST_SHOW_LABELS');
        $form_values['ST_LABEL_POSITION'] = Tools::getValue('ST_LABEL_POSITION');
        $form_values['ST_LABEL_BG_COLOR'] = Tools::getValue('ST_LABEL_BG_COLOR');
        $form_values['ST_LABEL_TEXT_COLOR'] = Tools::getValue('ST_LABEL_TEXT_COLOR');
        $form_values['ST_HIDE_ADD_TO_CART'] = Tools::getValue('ST_HIDE_ADD_TO_CART');
        $form_values['ST_SHOW_REMINDER'] = Tools::getValue('ST_SHOW_REMINDER');
        $form_values['ST_REMINDER_POSITION'] = Tools::getValue('ST_REMINDER_POSITION');
        $form_values['ST_HIDE_COMBINATIONS'] = Tools::getValue('ST_HIDE_COMBINATIONS');
        $form_values['ST_HIDE_QUANTITY'] = Tools::getValue('ST_HIDE_QUANTITY');
        $form_values['ST_AUTO_SWITCH_DEFAULT'] = Tools::getValue('ST_AUTO_SWITCH_DEFAULT');

        // Handle multilingual fields
        $languages = Language::getLanguages(false);
        $custom_labels = [];
        $success_messages = [];

        foreach ($languages as $lang) {
            $custom_labels[$lang['id_lang']] = Tools::getValue('ST_CUSTOM_LABEL_' . $lang['id_lang']);
            $success_messages[$lang['id_lang']] = Tools::getValue('ST_SUCCESS_MESSAGE_' . $lang['id_lang']);
        }

        $form_values['ST_CUSTOM_LABEL'] = $custom_labels;
        $form_values['ST_SUCCESS_MESSAGE'] = $success_messages;

        foreach ($form_values as $key => $value) {
            Configuration::updateValue($key, $value);
        }

        return $this->displayConfirmation($this->l('Settings updated'));
    }

    // Hook Methods
    public function hookDisplayHeader()
    {
        if (!Configuration::get('ST_ENABLE_MODULE')) {
            return;
        }

        $this->context->controller->addCSS($this->_path . 'views/css/front.css');
        $this->context->controller->addJS($this->_path . 'views/js/front.js');
    }

    public function hookDisplayProductActions($params)
    {
        if (!Configuration::get('ST_ENABLE_MODULE')) {
            return;
        }

        $product = $params['product'];

        if (!$this->isProductOutOfStock($product)) {
            return;
        }

        $this->context->smarty->assign([
            'product' => $product,
            'show_reminder' => Configuration::get('ST_SHOW_REMINDER'),
            'reminder_position' => Configuration::get('ST_REMINDER_POSITION'),
            'module_dir' => $this->_path
        ]);

        return $this->display(__FILE__, 'product-actions.tpl');
    }

    public function hookDisplayProductAdditionalInfo($params)
    {
        if (!Configuration::get('ST_ENABLE_MODULE') || !Configuration::get('ST_SHOW_LABELS')) {
            return;
        }

        $product = $params['product'];

        if (!$this->isProductOutOfStock($product)) {
            return;
        }

        $custom_settings = $this->getProductCustomSettings($product['id_product']);
        $label = $custom_settings['custom_label'] ?: Configuration::get('ST_CUSTOM_LABEL', $this->context->language->id);

        $this->context->smarty->assign([
            'out_of_stock_label' => $label,
            'label_position' => Configuration::get('ST_LABEL_POSITION'),
            'label_bg_color' => $custom_settings['label_color'] ?: Configuration::get('ST_LABEL_BG_COLOR'),
            'label_text_color' => $custom_settings['text_color'] ?: Configuration::get('ST_LABEL_TEXT_COLOR'),
            'product_id' => $product['id_product']
        ]);

        return $this->display(__FILE__, 'out-of-stock-label.tpl');
    }

    public function hookActionProductUpdate($params)
    {
        if (!Configuration::get('ST_ENABLE_MODULE') || !Configuration::get('ST_AUTO_SWITCH_DEFAULT')) {
            return;
        }

        $product = new Product($params['id_product']);
        $this->autoSwitchDefaultCombination($product);
    }

    public function hookActionUpdateQuantity($params)
    {
        if (!Configuration::get('ST_ENABLE_MODULE')) {
            return;
        }

        $product = new Product($params['id_product']);

        // Check if product is back in stock and send notifications
        if ($params['quantity'] > 0) {
            $this->sendRestockNotifications($params['id_product'], $params['id_product_attribute']);
        }

        // Auto switch default combination if needed
        if (Configuration::get('ST_AUTO_SWITCH_DEFAULT')) {
            $this->autoSwitchDefaultCombination($product);
        }
    }

    // Helper Methods
    protected function isProductOutOfStock($product)
    {
        if (is_array($product)) {
            $product_obj = new Product($product['id_product']);
        } else {
            $product_obj = $product;
        }

        return $product_obj->getQuantity($product_obj->id) <= 0;
    }

    protected function getProductCustomSettings($id_product)
    {
        $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'st_product_settings` WHERE `id_product` = ' . (int)$id_product;
        $result = Db::getInstance()->getRow($sql);

        return $result ?: [
            'custom_label' => null,
            'label_color' => null,
            'text_color' => null,
            'hide_add_to_cart' => 0,
            'show_reminder' => 1
        ];
    }

    protected function autoSwitchDefaultCombination($product)
    {
        $combinations = $product->getAttributeCombinations($this->context->language->id);

        if (empty($combinations)) {
            return;
        }

        $current_default = $product->getDefaultIdProductAttribute();
        $current_default_qty = StockAvailable::getQuantityAvailableByProduct($product->id, $current_default);

        if ($current_default_qty > 0) {
            return; // Current default is in stock
        }

        // Find first available combination
        foreach ($combinations as $combination) {
            $qty = StockAvailable::getQuantityAvailableByProduct($product->id, $combination['id_product_attribute']);
            if ($qty > 0) {
                $product->deleteDefaultAttributes();
                $product->setDefaultAttribute($combination['id_product_attribute']);
                break;
            }
        }
    }

    protected function sendRestockNotifications($id_product, $id_product_attribute = null)
    {
        $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'st_reminder_subscriptions`
                WHERE `id_product` = ' . (int)$id_product . '
                AND `notified` = 0';

        if ($id_product_attribute) {
            $sql .= ' AND (`id_product_attribute` = ' . (int)$id_product_attribute . ' OR `id_product_attribute` IS NULL)';
        }

        $subscriptions = Db::getInstance()->executeS($sql);

        foreach ($subscriptions as $subscription) {
            $this->sendNotificationEmail($subscription);

            // Mark as notified
            Db::getInstance()->update(
                'st_reminder_subscriptions',
                ['notified' => 1],
                'id_subscription = ' . (int)$subscription['id_subscription']
            );
        }
    }

    protected function sendNotificationEmail($subscription)
    {
        $product = new Product($subscription['id_product'], false, $this->context->language->id);

        $template_vars = [
            '{product_name}' => $product->name,
            '{product_link}' => $this->context->link->getProductLink($product),
            '{shop_name}' => Configuration::get('PS_SHOP_NAME')
        ];

        Mail::Send(
            $this->context->language->id,
            'restock_notification',
            $this->l('Product Back in Stock'),
            $template_vars,
            $subscription['email'],
            null,
            null,
            null,
            null,
            null,
            dirname(__FILE__) . '/mails/',
            false,
            null
        );
    }
}
