<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Back in Stock</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 2px solid #e0e0e0;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0;
        }
        .content {
            padding: 30px 0;
        }
        .product-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        .cta-button {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            padding: 20px 0;
            border-top: 1px solid #e0e0e0;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Great News!</h1>
        </div>
        
        <div class="content">
            <p>Hello,</p>
            
            <p>The product you were waiting for is now back in stock!</p>
            
            <div class="product-info">
                <h3>{product_name}</h3>
                <p>This product is now available for purchase.</p>
            </div>
            
            <p>Don't wait too long - popular items tend to sell out quickly!</p>
            
            <div style="text-align: center;">
                <a href="{product_link}" class="cta-button">View Product</a>
            </div>
            
            <p>Thank you for your patience and for choosing {shop_name}.</p>
            
            <p>Best regards,<br>
            The {shop_name} Team</p>
        </div>
        
        <div class="footer">
            <p>This email was sent because you requested to be notified when this product became available.</p>
            <p>&copy; {shop_name}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
